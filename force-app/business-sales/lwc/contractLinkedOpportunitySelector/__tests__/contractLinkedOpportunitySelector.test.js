import { flushPromises } from "c/utils";
import { createElement } from "lwc";
import contractLinkedOpportunitySelector from "c/contractLinkedOpportunitySelector";

const mockContract = {
    name: "test Other",
    soldBy: "<PERSON>",
    confirmType: "No Formal Contract",
    confirmDate: "2023-10-24",
    isAmendment: false,
    amendatoryType: "",
    extendFrom: "",
    description: "This is a description",
    startDate: "2023-10-20",
    endDate: "2023-11-20",
    currency: "CNY",
    amount: 100,
    estimatedDiscount: 10,
    expense: 20
};
const associatedOpportunity = {
    isLinkOneOpp: true,
    oppItems: []
};

describe("c-contract-linked-opportunity-selector", () => {
    let element;
    let shadowRoot;
    beforeEach(async () => {
        element = createElement("c-contract-linked-opportunity-selector", {
            is: contractLinkedOpportunitySelector
        });
        element.contract = mockContract;
        element.associatedOpportunity = associatedOpportunity;
        document.body.appendChild(element);
        shadowRoot = element.shadowRoot;
        await flushPromises();
    });

    afterEach(() => {
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it("should render component and values correctly", () => {
        const radioGroup = shadowRoot.querySelector("lightning-radio-group");
        const radioGroupOptions = radioGroup.options;
        expect(radioGroupOptions.length).toBe(2);
        expect(radioGroupOptions[0].label).toBe("One Opportunity");
        expect(radioGroupOptions[1].label).toBe("Multiple Opportunities");

        const oneOppInput = shadowRoot.querySelector("lightning-record-picker");
        expect(oneOppInput).toBeTruthy();
    });

    it("should handle radio change to 'One Opportunity' correctly", async () => {
        const radioGroup = shadowRoot.querySelector("lightning-radio-group");
        radioGroup.value = "Multiple Opportunities";
        const changeEvent = new CustomEvent("change", {
            detail: {
                value: "One Opportunity"
            }
        });

        const dispatchedEvent = jest.fn();
        element.addEventListener("contractopportunityupdate", dispatchedEvent);

        radioGroup.dispatchEvent(changeEvent);
        await flushPromises();

        expect(dispatchedEvent).toHaveBeenCalled();
        const oneOpportunityInput = shadowRoot.querySelector("lightning-record-picker");
        expect(oneOpportunityInput).toBeTruthy();
    });

    it("should handle one opportunity input change correctly", async () => {
        const inputField = shadowRoot.querySelector("lightning-record-picker");
        const inputEvent = new CustomEvent("change", {
            detail: {
                recordId: "opp-123456"
            }
        });

        const dispatchedEvent = jest.fn();
        element.addEventListener("contractopportunityupdate", dispatchedEvent);
        inputField.dispatchEvent(inputEvent);
        await flushPromises();

        expect(dispatchedEvent).toHaveBeenCalled();
    });

    it("should show correct component and set default input value and dispatch event when click radio button", async () => {
        element.contract = {
            Subtype__c: "Estimated Spend",
            Start_Date__c: "2023-10-31",
            End_Date__c: "2023-11-07",
            Name: "testA",
            accountId: "00119000014OrZvAAK"
        };
        element.associatedOpportunity = { isLinkOneOpp: false, oppItems: [{ opportunityId: "oppoId", opportunityContractValue: 100, opportunityCurrency: "USD", order: 1 }] };
        document.body.appendChild(element);
        const handleContractOpportunityUpdate = jest.fn();
        element.addEventListener("contractopportunityupdate", handleContractOpportunityUpdate);
        await flushPromises();

        let oppRadio = element.shadowRoot.querySelector("lightning-radio-group");
        oppRadio.dispatchEvent(new CustomEvent("change", { detail: { value: "One Opportunity" } }));
        await flushPromises();

        oppRadio = element.shadowRoot.querySelector("lightning-radio-group");
        const oppItems = element.shadowRoot.querySelector(".one-opportunity-input");
        expect(oppRadio.value).toBe("One Opportunity");
        expect(oppItems).toBeTruthy();
        expect(oppItems.associatedOpportunityItems).toBeFalsy();
        expect(handleContractOpportunityUpdate).toHaveBeenCalledTimes(1);

        oppRadio.dispatchEvent(new CustomEvent("change", { detail: { value: "multiple Opportunities" } }));
        await flushPromises();

        const oppItemsMutiple = element.shadowRoot.querySelector("c-contract-linked-opp-item");
        expect(oppRadio.value).toBe("Multiple Opportunities");
        expect(oppItemsMutiple).toBeTruthy();
        expect(oppItemsMutiple.associatedOpportunityItems).toMatchObject([
            {
                opportunityContractValue: 100,
                opportunityCurrency: "USD",
                opportunityId: "oppoId",
                order: 1
            }
        ]);
        expect(handleContractOpportunityUpdate).toHaveBeenCalledTimes(2);
    });
});
